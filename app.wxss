/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 50rpx;
  box-sizing: border-box;
} 

/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 通用按钮样式 */
.btn {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  margin: 10rpx 0;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn:active {
  transform: scale(0.95);
}

.btn-primary {
  background-color: #1296DB;
  color: white;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1rpx solid #ddd;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 表单样式 */
.form-group {
  margin: 30rpx 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 列表样式 */
.list-item {
  background-color: white;
  padding: 30rpx;
  margin: 10rpx 0;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.list-item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.list-item-meta {
  font-size: 24rpx;
  color: #999;
}

/* 状态标签 */

.status-label,.status-tag {
  display:inline-block !important;
  margin-bottom: 0 !important;
  font-size: 28rpx !important;
  border-radius: 20rpx !important;
  color: #333 !important;
  line-height: 20rpx !important;
  height: 60rpx !important;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 搜索栏通用样式 */
.search-bar {
  margin-bottom: 20rpx;
  position: relative;
}

.search-input {
  background: white;
  border-radius: 12rpx;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  box-sizing: border-box;
  width: 100%;
}

.search-input:focus {
  border-color: #1296DB;
  box-shadow: 0 2rpx 12rpx rgba(18, 150, 219, 0.2);
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }

  .card {
    padding: 20rpx;
    margin: 15rpx 0;
  }
}
